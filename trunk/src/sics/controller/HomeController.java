package sics.controller;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.lambda.AWSLambda;
import com.amazonaws.services.lambda.AWSLambdaClientBuilder;
import com.amazonaws.services.lambda.model.InvokeRequest;
import com.amazonaws.services.lambda.model.InvokeResult;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import jdk.nashorn.internal.objects.Global;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.SavedRequest;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;  //Classe per il mapping delle richieste
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.support.RequestContextUtils;

import sics.domain.Fixture;
import sics.domain.Team;
import sics.domain.User;
import sics.enums.StudioType;
import sics.helper.*;
import sics.listener.SessionListener;
import sics.service.UserService;
import sics.websocket.MessageType;
import sics.websocket.WebSocketEndpoint;

@Controller
@RequestMapping("/auth")
public class HomeController extends BaseController {

    private final static String pageLogin = "auth/login.jsp";
    private final static String pageRecoverPassword = "auth/recoverPassword.jsp";
    private final static String page404 = "auth/404.jsp";
    private final static String page403 = "auth/403.jsp";
    private final static String pageMultipleSession = "auth/multipleSession.jsp";
    private final static String pageMatchStudio = "auth/matchStudio.jsp";

    private final static String pageHome = "user/home.jsp";

    private final UserService uService = new UserService();

    @RequestMapping("/404")
    public String error404(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            this.initModule(session, model, request, response);
        }
        return page404;
    }

    @RequestMapping("/403")
    public String error403(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            this.initModule(session, model, request, response);
        }
        return page403;
    }

    @RequestMapping("/login")
    public String login(ModelMap model, HttpSession session,
            @RequestParam(value = "expired", required = false) Boolean expired, @RequestParam(value = "login_error", required = false) Boolean isError,
            HttpServletRequest request, HttpServletResponse response) {

        this.initModule(session, model, request, response);

        String strLocale = request.getLocale().getLanguage().toLowerCase();
        Locale loc;
        if (strLocale.equalsIgnoreCase("it")) {
            loc = new Locale(strLocale);
        } else {
            loc = new Locale("en");
        }

        // PER SVILUPPO CARICO DI BASE LE CREDENZIALI DI MARCO
        // DATO CHE OGNI VOLTA CHE SI MODIFICA IL CODICE VIENE KILLATA LA SESSIONE
        // COSA CHE INVECE SU SICS.TV NON FA... MEGLIO COSI' DATO CHE QUA CI SONO
        // DATI SENSIBILI
        boolean isLocal = GlobalHelper.isLocalEnvironment();
        if (isLocal && StringUtils.equals(request.getRemoteAddr(), "192.168.1.212")) {
//            model.addAttribute("usr", "<EMAIL>");
//            model.addAttribute("psw", "cvbLv4vo7A");
            model.addAttribute("usr", "<EMAIL>");
            model.addAttribute("psw", "marco");
        }

        SavedRequest savedRequest = new HttpSessionRequestCache().getRequest(request, response);

        RequestContextUtils.getLocaleResolver(request).setLocale(request, response, loc);
        if (savedRequest != null
                && !savedRequest.getParameterMap().isEmpty()
                && savedRequest.getParameterMap().get("ser") != null
                && !savedRequest.getParameterMap().get("ser")[0].isEmpty()
                && BooleanUtils.isNotTrue(isError)) {
            String ser = savedRequest.getParameterMap().get("ser")[0];
            String username, password;
            byte[] serDecode = Base64.decodeBase64(ser);
            String[] splitSer = new String(serDecode).split("##");
            if (splitSer.length == 2) {
                username = splitSer[0];
                password = splitSer[1];
                model.addAttribute("usr", username);
                model.addAttribute("psw", password);
            }
        }

        // parametro aggiunto dalla classe SessionInactiveFilter#doBeforeProcessing
        Boolean updated = false;
        if (session.getAttribute("updated") != null) {
            updated = StringUtils.equalsIgnoreCase(session.getAttribute("updated").toString(), "true");
        }
        model.addAttribute("mUpdated", BooleanUtils.isTrue(updated));

        return pageLogin;
    }

    @RequestMapping("/recoverPassword")
    public String recoverPassword(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        this.initModule(session, model, request, response);

        return pageRecoverPassword;
    }

    @RequestMapping("/logout")
    public String logout(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request) {
        this.initModule(session, model, request, response);
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);

        // parametro aggiunto dalla classe SessionInactiveFilter#doBeforeProcessing
        Boolean updated = false;
        if (session.getAttribute("updated") != null) {
            updated = StringUtils.equalsIgnoreCase(session.getAttribute("updated").toString(), "true");
        }
        model.addAttribute("mUpdated", BooleanUtils.isTrue(updated));

        if (curUser != null) {
            try {
                SecurityContextHolder.clearContext(); // Rimuove l'utente autenticato
                SessionListener.destroySession(session, curUser.getId());
            } catch (Exception ex) {
                GlobalHelper.sendExceptionMail(request, ex);
                Logger.getLogger(HomeController.class.getName()).log(Level.SEVERE, "/logout page. can't invalidate current session. Already invalidated ?");
            }

            GlobalHelper.writeLogData(session, GlobalHelper.kActionLogoutSession, curUser.getEmail(), curUser.getPassword(), curUser.getId());
        } else {
            Logger.getLogger(HomeController.class.getName()).log(Level.SEVERE, "/logout page. curUser is null");
        }

        return pageLogin;
    }

    @RequestMapping("/lostpwd")
    public @ResponseBody
    String lostpwd(@RequestParam("formUsername") String formUsername, HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request) {
        String result = "ko";
        this.initModule(session, model, request, response);
        if (StringUtils.isNotBlank(formUsername)) {
            User curUser = uService.getUserByMail(formUsername);

            if (curUser != null && curUser.getExpirationDate().after(new Date())) {
                uService.resetUserPassword(curUser.getId());
                // aggiorno per prendere la nuova password
                curUser = uService.getUserByMail(formUsername);

                String content = SpringApplicationContextHelper.getMessage("messages.reset.password.email", RequestContextUtils.getLocale(request));
                content += "<br/><br/><strong>Email</strong>: " + curUser.getEmail() + "<br/><strong>Password</strong>: " + curUser.getPassword();
                MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp", RequestContextUtils.getLocale(request)), SpringApplicationContextHelper.getMessage("email.smtp.port", RequestContextUtils.getLocale(request)), SpringApplicationContextHelper.getMessage("email.smtp.user", RequestContextUtils.getLocale(request)), SpringApplicationContextHelper.getMessage("email.smtp.pwd", RequestContextUtils.getLocale(request)));
                mail.sendMail(SpringApplicationContextHelper.getMessage("email.from", RequestContextUtils.getLocale(request)), curUser.getEmail(), null, "<EMAIL>", null, null, "SDA - Password Recover", content, null, "");
                result = "ok";
            }
        }

        return result;
    }

    @RequestMapping(value = "/updateTranslations")
    public @ResponseBody
    String updateTranslations(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request) {
        try {
            DynamicReloadableResourceBundleMessageSource.checkDatabase(true);
        } catch (NumberFormatException ex) {
            GlobalHelper.reportError(ex);
        }
        return "ok";
    }

    @RequestMapping("/multipleSession")
    public String multipleSession(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request) {

        this.initModule(session, model, request, response);
        return pageMultipleSession;
    }

    @RequestMapping("/matchStudio")
    public String matchStudio(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
            @RequestParam("fixtureId") Long fixtureId, @RequestParam(value = "playerIds", required = false) String playerIds,
            @RequestParam(value = "language", required = false) String language, @RequestParam(value = "isLambda", required = false) Boolean isLambda,
            @RequestParam(value = "data", required = false) String data) {
        try {
            this.initModule(session, model, request, response);

            Long userId = null;
            String email = null;
            try {
                if (StringUtils.isBlank(data)) {
                    return "auth/matchStudioExpired.jsp";
                }
                // ---- Decodifica (lettura) qui sotto
                String secret = GlobalHelper.CRYPT_SECRET_KEY;
                Key keyDecode = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
                Jws<Claims> parsedJwt = Jwts.parserBuilder()
                        .setSigningKey(keyDecode)
                        .build()
                        .parseClaimsJws(data);

                Claims claims = parsedJwt.getBody();
                if (claims.get("userId") == null || claims.getExpiration().before(new Date())) {
                    return "auth/matchStudioExpired.jsp";
                }

                userId = Long.valueOf(claims.get("userId").toString());
                email = claims.get("email").toString();
                GlobalHelper.writeLogData(session, GlobalHelper.kActionMatchStudioRequest, email, null, userId);
            } catch (Exception ex) {
                GlobalHelper.reportError(ex);
                return "redirect:/auth/404.htm";
            }
            
            // Check for infinite loop prevention
            Integer errorCount = (Integer) session.getAttribute("matchStudio_errorCount");
            if (errorCount != null && errorCount >= 1) {
                // Too many errors, redirect to 404
                session.removeAttribute("matchStudio_errorCount");
                return "redirect:/auth/404.htm";
            }

            // Prevent caching
            response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0");
            response.setHeader("Pragma", "no-cache");
            response.setDateHeader("Expires", 0);

            // Set locale for proper internationalization
            String sessionLanguage = "en";
            if (session.getAttribute(GlobalHelper.kBeanLanguage) != null) {
                sessionLanguage = StringUtils.defaultIfEmpty(session.getAttribute(GlobalHelper.kBeanLanguage).toString(), "en");
            }
            // If language parameter is provided, use it
            if (StringUtils.isNotBlank(language)) {
                sessionLanguage = language;
                session.setAttribute(GlobalHelper.kBeanLanguage, language);
            }
            Locale locale = BaseController.getLocaleFromLanguange(sessionLanguage);
            RequestContextUtils.getLocaleResolver(request).setLocale(request, response, locale);
            model.addAttribute("mLanguage", locale.getLanguage());

            // Store parameters in session for async processing
            session.setAttribute("matchStudio_fixtureId", fixtureId);
            session.setAttribute("matchStudio_playerIds", playerIds);
            session.setAttribute("matchStudio_language", language);
            session.setAttribute("matchStudio_isPlayerStudio", false);
            session.setAttribute("matchStudio_processing", true);
            session.setAttribute("matchStudio_isLambda", BooleanUtils.isTrue(isLambda));
            session.setAttribute("matchStudio_userId", userId);
            session.setAttribute("matchStudio_email", email);

            // Redirect to loading page
            return "auth/matchStudioLoading.jsp";
        } catch (Exception e) {
            GlobalHelper.reportError(e);
            return "redirect:/auth/404.htm";
        }
    }

    @RequestMapping("/matchStudio/process")
    @ResponseBody
    public String processMatchStudio(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request) {
        this.initModule(session, model, request, response);

        response.setContentType("application/json");
        // Prevent caching
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("Expires", 0);
        try {
            // Check if already processed
            if (session.getAttribute("matchStudio_processing") == null ||
                !(Boolean) session.getAttribute("matchStudio_processing")) {
                return "{\"status\":\"completed\",\"redirect\":\"/sicsdataanalytics/auth/matchStudio/view.htm\"}";
            }

            // Get parameters from session
            Long fixtureId = (Long) session.getAttribute("matchStudio_fixtureId");
            String playerIds = (String) session.getAttribute("matchStudio_playerIds");
            String language = (String) session.getAttribute("matchStudio_language");
            Boolean isPlayerStudio = (Boolean) session.getAttribute("matchStudio_isPlayerStudio");
            Boolean isLambda = (Boolean) session.getAttribute("matchStudio_isLambda");
            Long userId = (Long) session.getAttribute("matchStudio_userId");
            String email = (String) session.getAttribute("matchStudio_email");

            if (fixtureId == null) {
                return "{\"status\":\"error\",\"message\":\"Missing parameters\"}";
            }

            Fixture fixture = uService.getFixtureById(fixtureId);
            // check per sicurezza
            if (fixture != null) {
                if (fixture.getHomeTeamId() != null && fixture.getAwayTeamId() != null) {
                    Team homeTeam = teams.getOrDefault(fixture.getHomeTeamId(), null);
                    Team awayTeam = teams.getOrDefault(fixture.getAwayTeamId(), null);
                    if (homeTeam == null) {
                        homeTeam = uService.getTeam(fixture.getHomeTeamId());
                    }
                    if (awayTeam == null) {
                        awayTeam = uService.getTeam(fixture.getAwayTeamId());
                    }

                    // per sicurezza se non passo la lingua mostro en
                    language = StringUtils.defaultIfEmpty(language, "en");
                    playerIds = StringUtils.defaultIfEmpty(playerIds, "none");

                    String folderHashInput = fixtureId + "-" + playerIds;
                    String key = fixtureId + "/" + GlobalHelper.fixedLengthHash(folderHashInput, 20) + "/" + homeTeam.getName(language) + "-" + awayTeam.getName(language) + "-" + DateHelper.toStringShortNoSlash(fixture.getGameDate()) + "_" + language + ".pdf";

                    // vedo ora se esiste già il pdf
                    boolean exists = GlobalHelper.objectExists(GlobalHelper.kBucketReport, key);
                    if (exists && !isLambda) {
                        // devo fare la get del path e fare redirect
                        String pathS3 = GlobalHelper.pathMatchReportS3(key);
                        if (StringUtils.isNotBlank(pathS3)) {
                            return "{\"status\":\"completed\",\"redirect\":\"" + pathS3 + "\"}";
                        } else {
                            // redirect a 404
                            return "{\"status\":\"error\",\"message\":\"Processing failed\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
                        }
                    } else {
                        if (isLambda) {
                            // Initialize MatchStudioHelper if needed
                            if (MatchStudioHelper.kServletContextPathImages == null) {
                                MatchStudioHelper.kServletContextPathImages = session.getServletContext().getRealPath("/images") + File.separator;
                                MatchStudioHelper.initialize();
                            }

                            // Parse player IDs
                            List<Long> playerIdList = new ArrayList<>();
                            if (StringUtils.isNotBlank(playerIds)) {
                                String[] split = playerIds.split(",");
                                for (String playerId : split) {
                                    if (StringUtils.isNotBlank(playerId)) {
                                        try {
                                            playerIdList.add(Long.parseLong(playerId));
                                        } catch (NumberFormatException ex) {
                                            GlobalHelper.reportError(ex);
                                        }
                                    }
                                }
                            }

                            // Process the match studio data and store in session
                            String result = MatchStudioController.matchStudio(response, model, session, request, fixtureId, playerIdList, language, isPlayerStudio);

                            // Store the processed model attributes in session for the view
                            session.setAttribute("matchStudio_modelAttributes", model);
                            session.setAttribute("matchStudio_processing", false);

                            return "{\"status\":\"completed\",\"redirect\":\"/sicsdataanalytics/auth/matchStudio/view.htm\"}";
                        } else {
                            // bisogna lanciare la lambda
                            try {
                                GlobalHelper.writeLogData(session, GlobalHelper.kActionMatchStudioLamba, email, null, userId);

                                // key = GlobalHelper.fixedLengthHash(folderHashInput, 20) + "/" + homeTeam.getName(language) + "-" + awayTeam.getName(language) + "-" + DateHelper.toStringShortNoSlash(fixture.getGameDate());
                                String payload = GlobalHelper.generatePayload(fixtureId, playerIds, language, key);
                                InvokeRequest invokeRequest = new InvokeRequest()
                                        .withFunctionName(GlobalHelper.LAMBDA_MATCHSTUDIO_FUNCTION_NAME)
                                        .withPayload(payload);
                                InvokeResult invokeResult = null;

                                AWSCredentials credentials = new BasicAWSCredentials(GlobalHelper.LAMBDA_USER_ACCESS_KEY, GlobalHelper.LAMBDA_USER_SECRET_KEY);
                                ClientConfiguration config = new ClientConfiguration();
                                config.setConnectionTimeout(GlobalHelper.MAX_TIMEOUT_CONNECTION_MATCHSTUDIO);
                                config.setSocketTimeout(GlobalHelper.MAX_TIMEOUT_CONNECTION_MATCHSTUDIO);
                                AWSLambda awsLambda = AWSLambdaClientBuilder.standard()
                                        .withCredentials(new AWSStaticCredentialsProvider(credentials))
                                        .withRegion(Regions.EU_WEST_1)
                                        .withClientConfiguration(config)
                                        .build();

                                // long startX = System.currentTimeMillis();
                                invokeResult = awsLambda.invoke(invokeRequest);
                                // System.out.println("INVOKE TIME: " + getTimeString(System.currentTimeMillis() - startX));
                                if (invokeResult != null) {
                                    String lambdaResponse = new String(invokeResult.getPayload().array(), StandardCharsets.UTF_8);
                                    System.out.println("Response Code:" + invokeResult.getStatusCode());
                                    if (invokeResult.getStatusCode() == 200) {
                                        if (lambdaResponse.contains("\"statusCode\":200")) {
                                            try {
                                                String pathS3 = lambdaResponse.replace("{\"statusCode\":200,\"body\":\"{\\\"message\\\":\\\"", "").replace("\\\"}\"", "").replace("}", "");
                                                if (StringUtils.isNotBlank(pathS3)) {
                                                    return "{\"status\":\"completed\",\"redirect\":\"" + pathS3 + "\"}";
                                                }
                                            } catch (Exception e) {
                                                System.err.println(lambdaResponse);
                                                return "{\"status\":\"error\",\"message\":\"Processing failed\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
                                            }
                                        } else {
                                            System.err.println(lambdaResponse);
                                            return "{\"status\":\"error\",\"message\":\"Processing failed\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
                                        }
                                    } else {
                                        System.err.println(lambdaResponse);
                                        return "{\"status\":\"error\",\"message\":\"Processing failed\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
                                    }
                                } else {
                                    System.err.println("ERROR InvokeResult NULL - something went wrong");
                                    return "{\"status\":\"error\",\"message\":\"Processing failed\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                                return "{\"status\":\"error\",\"message\":\"Processing failed\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            GlobalHelper.reportError(e);
            session.setAttribute("matchStudio_processing", false);
            // Clear all session data to prevent infinite loops
            session.removeAttribute("matchStudio_modelAttributes");
            session.removeAttribute("matchStudio_fixtureId");
            session.removeAttribute("matchStudio_playerIds");
            session.removeAttribute("matchStudio_language");
            session.removeAttribute("matchStudio_isPlayerStudio");
            session.removeAttribute("matchStudio_isLambda");
            // Increment error count
            Integer errorCount = (Integer) session.getAttribute("matchStudio_errorCount");
            if (errorCount == null) {
                errorCount = 0;
            }
            errorCount++;
            session.setAttribute("matchStudio_errorCount", errorCount);
            return "{\"status\":\"error\",\"message\":\"Processing failed: " + e.getMessage() + "\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
        }

        // se qualcosa non è andato bene allora 404
        return "{\"status\":\"error\",\"message\":\"Processing failed\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
    }

    @RequestMapping("/matchStudio/status")
    @ResponseBody
    public String checkMatchStudioStatus(HttpServletResponse response, HttpSession session) {
        response.setContentType("application/json");
        // Prevent caching
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("Expires", 0);
        try {
            Boolean processing = (Boolean) session.getAttribute("matchStudio_processing");

            if (processing == null || !processing) {
                // Processing is complete - build redirect URL with parameters for refresh support
                Long fixtureId = (Long) session.getAttribute("matchStudio_fixtureId");
                String playerIds = (String) session.getAttribute("matchStudio_playerIds");
                String language = (String) session.getAttribute("matchStudio_language");

                String redirectUrl = "/sicsdataanalytics/auth/matchStudio/view.htm";
                if (fixtureId != null) {
                    redirectUrl += "?fixtureId=" + fixtureId;
                    if (StringUtils.isNotBlank(playerIds)) {
                        redirectUrl += "&playerIds=" + playerIds;
                    }
                    if (StringUtils.isNotBlank(language)) {
                        redirectUrl += "&language=" + language;
                    }
                }
                return "{\"status\":\"completed\",\"redirect\":\"" + redirectUrl + "\"}";
            } else {
                // Still processing
                return "{\"status\":\"processing\"}";
            }
        } catch (Exception e) {
            GlobalHelper.reportError(e);
            // Clear session data and redirect to 404 on error
            session.removeAttribute("matchStudio_modelAttributes");
            session.removeAttribute("matchStudio_fixtureId");
            session.removeAttribute("matchStudio_playerIds");
            session.removeAttribute("matchStudio_language");
            session.removeAttribute("matchStudio_isPlayerStudio");
            session.removeAttribute("matchStudio_processing");
            session.removeAttribute("matchStudio_isLambda");
            return "{\"status\":\"error\",\"message\":\"Status check failed\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
        }
    }

    @RequestMapping("/matchStudio/view")
    public String viewMatchStudio(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request) throws IOException {
        try {
            this.initModule(session, model, request, response);

            // Prevent caching
            response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0");
            response.setHeader("Pragma", "no-cache");
            response.setDateHeader("Expires", 0);

            // Retrieve processed model attributes from session
            ModelMap storedModel = (ModelMap) session.getAttribute("matchStudio_modelAttributes");
            if (storedModel != null) {
                model.addAllAttributes(storedModel);

                // Store parameters for potential refresh
                Long fixtureId = (Long) session.getAttribute("matchStudio_fixtureId");
                String playerIds = (String) session.getAttribute("matchStudio_playerIds");
                String language = (String) session.getAttribute("matchStudio_language");

                // Clean up session including error count
                session.removeAttribute("matchStudio_errorCount");
                session.removeAttribute("matchStudio_modelAttributes");
                session.removeAttribute("matchStudio_fixtureId");
                session.removeAttribute("matchStudio_playerIds");
                session.removeAttribute("matchStudio_language");
                session.removeAttribute("matchStudio_isPlayerStudio");
                session.removeAttribute("matchStudio_processing");
                session.removeAttribute("matchStudio_isLambda");

                // Set locale for proper internationalization
                String sessionLanguage = "en";
                if (session.getAttribute(GlobalHelper.kBeanLanguage) != null) {
                    sessionLanguage = StringUtils.defaultIfEmpty(session.getAttribute(GlobalHelper.kBeanLanguage).toString(), "en");
                }
                // If language parameter is provided, use it
                if (StringUtils.isNotBlank(language)) {
                    sessionLanguage = language;
                    session.setAttribute(GlobalHelper.kBeanLanguage, language);
                }
                Locale locale = BaseController.getLocaleFromLanguange(sessionLanguage);
                RequestContextUtils.getLocaleResolver(request).setLocale(request, response, locale);
                model.addAttribute("mLanguage", locale.getLanguage());

                return pageMatchStudio;
            } else {
                // No data available, try to get parameters from request if available
                String fixtureId = request.getParameter("fixtureId");
                String playerIds = request.getParameter("playerIds");
                String language = request.getParameter("language");

                if (StringUtils.isNotBlank(fixtureId)) {
                    // Set locale if language parameter is provided
                    if (StringUtils.isNotBlank(language)) {
                        session.setAttribute(GlobalHelper.kBeanLanguage, language);
                        Locale locale = BaseController.getLocaleFromLanguange(language);
                        RequestContextUtils.getLocaleResolver(request).setLocale(request, response, locale);
                    }

                    // Redirect with parameters (only if we haven't exceeded error limit)
                    String redirectUrl = "/sicsdataanalytics/auth/matchStudio.htm?fixtureId=" + fixtureId;
                    if (StringUtils.isNotBlank(playerIds)) {
                        redirectUrl += "&playerIds=" + playerIds;
                    }
                    if (StringUtils.isNotBlank(language)) {
                        redirectUrl += "&language=" + language;
                    }
                    response.sendRedirect(redirectUrl);
                    return redirectUrl;
                } else {
                    // Too many errors or no parameters available, redirect to 404
                    session.removeAttribute("matchStudio_errorCount");
                    response.sendRedirect("/sicsdataanalytics/auth/404.htm");
                    return "redirect:/auth/404.htm";
                }
            }
        } catch (Exception e) {
            // Log the error and redirect to 404 to prevent infinite loops
            GlobalHelper.reportError(e);
            session.removeAttribute("matchStudio_errorCount");
            session.removeAttribute("matchStudio_modelAttributes");
            session.removeAttribute("matchStudio_fixtureId");
            session.removeAttribute("matchStudio_playerIds");
            session.removeAttribute("matchStudio_language");
            session.removeAttribute("matchStudio_isPlayerStudio");
            session.removeAttribute("matchStudio_processing");
            session.removeAttribute("matchStudio_isLambda");
            response.sendRedirect("/sicsdataanalytics/auth/404.htm");
            return "redirect:/auth/404.htm";
        }
    }

    @RequestMapping("/playerStudio")
    public String playerStudio(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
                               @RequestParam("fixtureId") Long fixtureId, @RequestParam("playerIds") String playerIds,
                               @RequestParam(value = "language", required = false) String language,
                               @RequestParam(value = "isLambda", required = false) Boolean isLambda,
                               @RequestParam(value = "data", required = false) String data) {
        try {
            this.initModule(session, model, request, response);

            Long userId = null;
            String email = null;
            try {
                if (StringUtils.isBlank(data)) {
                    return "auth/matchStudioExpired.jsp";
                }
                // ---- Decodifica (lettura) qui sotto
                String secret = GlobalHelper.CRYPT_SECRET_KEY;
                Key keyDecode = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
                Jws<Claims> parsedJwt = Jwts.parserBuilder()
                        .setSigningKey(keyDecode)
                        .build()
                        .parseClaimsJws(data);

                Claims claims = parsedJwt.getBody();
                if (claims.get("userId") == null || claims.getExpiration().before(new Date())) {
                    return "auth/matchStudioExpired.jsp";
                }

                userId = Long.valueOf(claims.get("userId").toString());
                email = claims.get("email").toString();
                GlobalHelper.writeLogData(session, GlobalHelper.kActionPlayerStudioRequest, email, null, userId);
            } catch (Exception ex) {
                GlobalHelper.reportError(ex);
                return "redirect:/auth/404.htm";
            }

            // Check for infinite loop prevention
            Integer errorCount = (Integer) session.getAttribute("playerStudio_errorCount");
            if (errorCount != null && errorCount >= 1) {
                // Too many errors, redirect to 404
                session.removeAttribute("playerStudio_errorCount");
                return "redirect:/auth/404.htm";
            }

            // Prevent caching
            response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0");
            response.setHeader("Pragma", "no-cache");
            response.setDateHeader("Expires", 0);

            // Set locale for proper internationalization
            String sessionLanguage = "en";
            if (session.getAttribute(GlobalHelper.kBeanLanguage) != null) {
                sessionLanguage = StringUtils.defaultIfEmpty(session.getAttribute(GlobalHelper.kBeanLanguage).toString(), "en");
            }
            // If language parameter is provided, use it
            if (StringUtils.isNotBlank(language)) {
                sessionLanguage = language;
                session.setAttribute(GlobalHelper.kBeanLanguage, language);
            }
            Locale locale = BaseController.getLocaleFromLanguange(sessionLanguage);
            RequestContextUtils.getLocaleResolver(request).setLocale(request, response, locale);
            model.addAttribute("mLanguage", locale.getLanguage());

            // Store parameters in session for async processing
            session.setAttribute("matchStudio_fixtureId", fixtureId);
            session.setAttribute("matchStudio_playerIds", playerIds);
            session.setAttribute("matchStudio_language", language);
            session.setAttribute("matchStudio_isPlayerStudio", true);
            session.setAttribute("matchStudio_processing", true);
            session.setAttribute("matchStudio_isLambda", BooleanUtils.isTrue(isLambda));
            session.setAttribute("matchStudio_userId", userId);
            session.setAttribute("matchStudio_email", email);

            // Redirect to loading page
            return "auth/matchStudioLoading.jsp";
        } catch (Exception e) {
            GlobalHelper.reportError(e);
            return "redirect:/auth/404.htm";
        }
    }

    @RequestMapping("/playerStudio/process")
    @ResponseBody
    public String processPlayerStudio(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request) {
        this.initModule(session, model, request, response);

        response.setContentType("application/json");
        // Prevent caching
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("Expires", 0);
        try {
            // Check if already processed
            if (session.getAttribute("matchStudio_processing") == null ||
                !(Boolean) session.getAttribute("matchStudio_processing")) {
                return "{\"status\":\"completed\",\"redirect\":\"/sicsdataanalytics/auth/playerStudio/view.htm\"}";
            }

            // Get parameters from session
            Long fixtureId = (Long) session.getAttribute("matchStudio_fixtureId");
            String playerIds = (String) session.getAttribute("matchStudio_playerIds");
            String language = (String) session.getAttribute("matchStudio_language");
            Boolean isPlayerStudio = (Boolean) session.getAttribute("matchStudio_isPlayerStudio");
            Boolean isLambda = (Boolean) session.getAttribute("matchStudio_isLambda");
            Long userId = (Long) session.getAttribute("matchStudio_userId");
            String email = (String) session.getAttribute("matchStudio_email");

            if (fixtureId == null) {
                return "{\"status\":\"error\",\"message\":\"Missing parameters\"}";
            }

            Fixture fixture = uService.getFixtureById(fixtureId);
            // check per sicurezza
            if (fixture != null) {
                if (fixture.getHomeTeamId() != null && fixture.getAwayTeamId() != null) {
                    Team homeTeam = teams.getOrDefault(fixture.getHomeTeamId(), null);
                    Team awayTeam = teams.getOrDefault(fixture.getAwayTeamId(), null);
                    if (homeTeam == null) {
                        homeTeam = uService.getTeam(fixture.getHomeTeamId());
                    }
                    if (awayTeam == null) {
                        awayTeam = uService.getTeam(fixture.getAwayTeamId());
                    }

                    // per sicurezza se non passo la lingua mostro en
                    language = StringUtils.defaultIfEmpty(language, "en");
                    playerIds = StringUtils.defaultIfEmpty(playerIds, "none");

                    String folderHashInput = fixtureId + "-" + playerIds;
                    String key = fixtureId + "/" + GlobalHelper.fixedLengthHash(folderHashInput, 20) + "/PlayerStudio-" + homeTeam.getName(language) + "-" + awayTeam.getName(language) + "-" + DateHelper.toStringShortNoSlash(fixture.getGameDate()) + "_" + language + ".pdf";

                    // vedo ora se esiste già il pdf
                    boolean exists = GlobalHelper.objectExists(GlobalHelper.kBucketReport, key);
                    if (exists && !isLambda) {
                        // devo fare la get del path e fare redirect
                        String pathS3 = GlobalHelper.pathMatchReportS3(key);
                        if (StringUtils.isNotBlank(pathS3)) {
                            return "{\"status\":\"completed\",\"redirect\":\"" + pathS3 + "\"}";
                        } else {
                            // redirect a 404
                            return "{\"status\":\"error\",\"message\":\"Processing failed\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
                        }
                    } else {
                        if (isLambda) {
                            // Initialize MatchStudioHelper if needed
                            if (MatchStudioHelper.kServletContextPathImages == null) {
                                MatchStudioHelper.kServletContextPathImages = session.getServletContext().getRealPath("/images") + File.separator;
                                MatchStudioHelper.initialize();
                            }

                            // Parse player IDs
                            List<Long> playerIdList = new ArrayList<>();
                            if (StringUtils.isNotBlank(playerIds)) {
                                String[] split = playerIds.split(",");
                                for (String playerId : split) {
                                    if (StringUtils.isNotBlank(playerId)) {
                                        try {
                                            playerIdList.add(Long.parseLong(playerId));
                                        } catch (NumberFormatException ex) {
                                            GlobalHelper.reportError(ex);
                                        }
                                    }
                                }
                            }

                            // Process the player studio data and store in session
                            String result = MatchStudioController.matchStudio(response, model, session, request, fixtureId, playerIdList, language, isPlayerStudio);

                            // Store the processed model attributes in session for the view
                            session.setAttribute("matchStudio_modelAttributes", model);
                            session.setAttribute("matchStudio_processing", false);

                            return "{\"status\":\"completed\",\"redirect\":\"/sicsdataanalytics/auth/playerStudio/view.htm\"}";
                        } else {
                            // bisogna lanciare la lambda
                            try {
                                GlobalHelper.writeLogData(session, GlobalHelper.kActionPlayerStudioLamba, email, null, userId);
                                // key = GlobalHelper.fixedLengthHash(folderHashInput, 20) + "/PlayerStudio" + homeTeam.getName(language) + "-" + awayTeam.getName(language) + "-" + DateHelper.toStringShortNoSlash(fixture.getGameDate());
                                String payload = GlobalHelper.generatePayload(fixtureId, playerIds, language, key);
                                InvokeRequest invokeRequest = new InvokeRequest()
                                        .withFunctionName(GlobalHelper.LAMBDA_PLAYERSTUDIO_FUNCTION_NAME)
                                        .withPayload(payload);
                                InvokeResult invokeResult = null;

                                AWSCredentials credentials = new BasicAWSCredentials(GlobalHelper.LAMBDA_USER_ACCESS_KEY, GlobalHelper.LAMBDA_USER_SECRET_KEY);
                                ClientConfiguration config = new ClientConfiguration();
                                config.setConnectionTimeout(GlobalHelper.MAX_TIMEOUT_CONNECTION_PLAYERSTUDIO);
                                config.setSocketTimeout(GlobalHelper.MAX_TIMEOUT_CONNECTION_PLAYERSTUDIO);
                                AWSLambda awsLambda = AWSLambdaClientBuilder.standard()
                                        .withCredentials(new AWSStaticCredentialsProvider(credentials))
                                        .withRegion(Regions.EU_WEST_1)
                                        .withClientConfiguration(config)
                                        .build();

                                // long startX = System.currentTimeMillis();
                                invokeResult = awsLambda.invoke(invokeRequest);
                                // System.out.println("INVOKE TIME: " + getTimeString(System.currentTimeMillis() - startX));
                                if (invokeResult != null) {
                                    String lambdaResponse = new String(invokeResult.getPayload().array(), StandardCharsets.UTF_8);
                                    System.out.println("Response Code:" + invokeResult.getStatusCode());
                                    if (invokeResult.getStatusCode() == 200) {
                                        if (lambdaResponse.contains("\"statusCode\":200")) {
                                            try {
                                                String pathS3 = lambdaResponse.replace("{\"statusCode\":200,\"body\":\"{\\\"message\\\":\\\"", "").replace("\\\"}\"", "").replace("}", "");
                                                if (StringUtils.isNotBlank(pathS3)) {
                                                    return "{\"status\":\"completed\",\"redirect\":\"" + pathS3 + "\"}";
                                                }
                                            } catch (Exception e) {
                                                System.err.println(lambdaResponse);
                                                return "{\"status\":\"error\",\"message\":\"Processing failed\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
                                            }
                                        } else {
                                            System.err.println(lambdaResponse);
                                            return "{\"status\":\"error\",\"message\":\"Processing failed\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
                                        }
                                    } else {
                                        System.err.println(lambdaResponse);
                                        return "{\"status\":\"error\",\"message\":\"Processing failed\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
                                    }
                                } else {
                                    System.err.println("ERROR InvokeResult NULL - something went wrong");
                                    return "{\"status\":\"error\",\"message\":\"Processing failed\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                                return "{\"status\":\"error\",\"message\":\"Processing failed\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            GlobalHelper.reportError(e);
            session.setAttribute("matchStudio_processing", false);
            // Clear all session data to prevent infinite loops
            session.removeAttribute("matchStudio_modelAttributes");
            session.removeAttribute("matchStudio_fixtureId");
            session.removeAttribute("matchStudio_playerIds");
            session.removeAttribute("matchStudio_language");
            session.removeAttribute("matchStudio_isPlayerStudio");
            session.removeAttribute("matchStudio_isLambda");
            // Increment error count
            Integer errorCount = (Integer) session.getAttribute("playerStudio_errorCount");
            if (errorCount == null) {
                errorCount = 0;
            }
            errorCount++;
            session.setAttribute("playerStudio_errorCount", errorCount);
            return "{\"status\":\"error\",\"message\":\"Processing failed: " + e.getMessage() + "\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
        }

        // se qualcosa non è andato bene allora 404
        return "{\"status\":\"error\",\"message\":\"Processing failed\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
    }

    @RequestMapping("/playerStudio/status")
    @ResponseBody
    public String checkPlayerStudioStatus(HttpServletResponse response, HttpSession session) {
        response.setContentType("application/json");
        // Prevent caching
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("Expires", 0);
        try {
            Boolean processing = (Boolean) session.getAttribute("matchStudio_processing");

            if (processing == null || !processing) {
                // Processing is complete - build redirect URL with parameters for refresh support
                Long fixtureId = (Long) session.getAttribute("matchStudio_fixtureId");
                String playerIds = (String) session.getAttribute("matchStudio_playerIds");
                String language = (String) session.getAttribute("matchStudio_language");

                String redirectUrl = "/sicsdataanalytics/auth/playerStudio/view.htm";
                if (fixtureId != null && StringUtils.isNotBlank(playerIds)) {
                    redirectUrl += "?fixtureId=" + fixtureId + "&playerIds=" + playerIds;
                    if (StringUtils.isNotBlank(language)) {
                        redirectUrl += "&language=" + language;
                    }
                }
                return "{\"status\":\"completed\",\"redirect\":\"" + redirectUrl + "\"}";
            } else {
                // Still processing
                return "{\"status\":\"processing\"}";
            }
        } catch (Exception e) {
            GlobalHelper.reportError(e);
            // Clear session data and redirect to 404 on error
            session.removeAttribute("matchStudio_modelAttributes");
            session.removeAttribute("matchStudio_fixtureId");
            session.removeAttribute("matchStudio_playerIds");
            session.removeAttribute("matchStudio_language");
            session.removeAttribute("matchStudio_isPlayerStudio");
            session.removeAttribute("matchStudio_processing");
            session.removeAttribute("matchStudio_isLambda");
            return "{\"status\":\"error\",\"message\":\"Status check failed\",\"redirect\":\"/sicsdataanalytics/auth/404.htm\"}";
        }
    }

    @RequestMapping("/playerStudio/view")
    public String viewPlayerStudio(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request) throws IOException {
        try {
            this.initModule(session, model, request, response);

            // Prevent caching
            response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0");
            response.setHeader("Pragma", "no-cache");
            response.setDateHeader("Expires", 0);

            // Retrieve processed model attributes from session
            ModelMap storedModel = (ModelMap) session.getAttribute("matchStudio_modelAttributes");
            if (storedModel != null) {
                model.addAllAttributes(storedModel);

                // Store parameters for potential refresh
                Long fixtureId = (Long) session.getAttribute("matchStudio_fixtureId");
                String playerIds = (String) session.getAttribute("matchStudio_playerIds");
                String language = (String) session.getAttribute("matchStudio_language");

                // Clean up session including error count
                session.removeAttribute("playerStudio_errorCount");
                session.removeAttribute("matchStudio_modelAttributes");
                session.removeAttribute("matchStudio_fixtureId");
                session.removeAttribute("matchStudio_playerIds");
                session.removeAttribute("matchStudio_language");
                session.removeAttribute("matchStudio_isPlayerStudio");
                session.removeAttribute("matchStudio_processing");
                session.removeAttribute("matchStudio_isLambda");

                // Set locale for proper internationalization
                String sessionLanguage = "en";
                if (session.getAttribute(GlobalHelper.kBeanLanguage) != null) {
                    sessionLanguage = StringUtils.defaultIfEmpty(session.getAttribute(GlobalHelper.kBeanLanguage).toString(), "en");
                }
                // If language parameter is provided, use it
                if (StringUtils.isNotBlank(language)) {
                    sessionLanguage = language;
                    session.setAttribute(GlobalHelper.kBeanLanguage, language);
                }
                Locale locale = BaseController.getLocaleFromLanguange(sessionLanguage);
                RequestContextUtils.getLocaleResolver(request).setLocale(request, response, locale);
                model.addAttribute("mLanguage", locale.getLanguage());

                return pageMatchStudio;
            } else {
                // No data available, try to get parameters from request if available
                String fixtureId = request.getParameter("fixtureId");
                String playerIds = request.getParameter("playerIds");
                String language = request.getParameter("language");

                if (StringUtils.isNotBlank(fixtureId) && StringUtils.isNotBlank(playerIds)) {
                    // Set locale if language parameter is provided
                    if (StringUtils.isNotBlank(language)) {
                        session.setAttribute(GlobalHelper.kBeanLanguage, language);
                        Locale locale = BaseController.getLocaleFromLanguange(language);
                        RequestContextUtils.getLocaleResolver(request).setLocale(request, response, locale);
                    }

                    // Redirect with parameters (only if we haven't exceeded error limit)
                    String redirectUrl = "/sicsdataanalytics/auth/playerStudio.htm?fixtureId=" + fixtureId + "&playerIds=" + playerIds;
                    if (StringUtils.isNotBlank(language)) {
                        redirectUrl += "&language=" + language;
                    }
                    response.sendRedirect(redirectUrl);
                    return redirectUrl;
                } else {
                    // Too many errors or no parameters available, redirect to 404
                    session.removeAttribute("playerStudio_errorCount");
                    response.sendRedirect("/sicsdataanalytics/auth/404.htm");
                    return "redirect:/auth/404.htm";
                }
            }
        } catch (Exception e) {
            // Log the error and redirect to 404 to prevent infinite loops
            GlobalHelper.reportError(e);
            session.removeAttribute("playerStudio_errorCount");
            session.removeAttribute("matchStudio_modelAttributes");
            session.removeAttribute("matchStudio_fixtureId");
            session.removeAttribute("matchStudio_playerIds");
            session.removeAttribute("matchStudio_language");
            session.removeAttribute("matchStudio_isPlayerStudio");
            session.removeAttribute("matchStudio_processing");
            session.removeAttribute("matchStudio_isLambda");
            response.sendRedirect("/sicsdataanalytics/auth/404.htm");
            return "redirect:/auth/404.htm";
        }
    }

    @RequestMapping(value = "/wsbroadcast")
    public @ResponseBody
    String wsbroadcast(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
            @RequestParam("token") String token, @RequestParam("text") String text, @RequestParam("type") String type) {
        if (StringUtils.isNotBlank(token) && StringUtils.isNotBlank(text)) {
            if (StringUtils.equals(token, "sicswsbroadcast")) {
                MessageType messageType = MessageType.INFO;
                if (StringUtils.isNotBlank(type)) {
                    if (MessageType.valueOf(type) != null) {
                        messageType = MessageType.valueOf(type);
                    }
                }
                WebSocketEndpoint.broadcast(messageType, text);
            }
        }
        return "ok";
    }
}
