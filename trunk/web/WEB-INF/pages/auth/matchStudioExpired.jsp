<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
    response.setHeader("Access-Control-Allow-Origin", "*");
    response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" href="/sicsdataanalytics/images/favicon.ico"/>
    <title>Match Studio - Request Expired</title>

    <!-- CSS -->
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/assets/fonts/inter/inter.css" rel="stylesheet" type="text/css">
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/assets/icons/phosphor/styles.min.css" rel="stylesheet" type="text/css">
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/assets/icons/icomoon/styles.min.css" rel="stylesheet" type="text/css">
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/html/layout_1/full/assets/css/ltr/all.min.css" rel="stylesheet" type="text/css">

    <!-- JavaScript -->
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/jquery/jquery.min.js"></script>
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/notifications/noty.min.js"></script>
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/notifications/sweet_alert.min.js"></script>

    <style>
        body {
            background: linear-gradient(135deg, #1b2024 0%, #2d3436 100%);
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }

        .expired-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            text-align: center;
            color: white;
        }

        .logo {
            margin-bottom: 40px;
        }

        .logo img {
            max-width: 200px;
            height: auto;
        }

        .expired-content {
            max-width: 600px;
            padding: 0 20px;
        }

        .expired-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ff6b6b, #e74c3c);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .expired-subtitle {
            font-size: 1.4rem;
            margin-bottom: 30px;
            opacity: 0.9;
            line-height: 1.6;
            font-weight: 600;
        }

        .expired-message {
            font-size: 1.1rem;
            margin-bottom: 40px;
            opacity: 0.8;
            line-height: 1.6;
        }

        .expired-icon {
            margin-bottom: 30px;
        }

        .expired-icon i {
            font-size: 4rem;
            color: #ff6b6b;
            opacity: 0.8;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        .action-button {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            min-width: 140px;
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(116, 185, 255, 0.3);
            color: white;
            text-decoration: none;
        }

        .action-button.secondary {
            background: linear-gradient(45deg, #636e72, #2d3436);
        }

        .action-button.secondary:hover {
            box-shadow: 0 8px 25px rgba(99, 110, 114, 0.3);
        }
    </style>
</head>

<body>
    <div class="expired-container">
        <div class="logo">
            <img src="/sicsdataanalytics/images/logosics.png" alt="SICS Logo">
        </div>

        <div class="expired-content">
            <div class="expired-icon">
                <i class="ph-clock-countdown"></i>
            </div>

            <h1 class="expired-title">
                <spring:message code="match.studio.expired.title"/>
            </h1>

            <p class="expired-subtitle">
                <spring:message code="match.studio.expired.subtitle"/>
            </p>

            <p class="expired-message">
                <spring:message code="match.studio.expired.message"/>
            </p>

            <div class="action-buttons">
                <a href="/sicsdataanalytics/user/home.htm" class="action-button">
                    <spring:message code="match.studio.expired.go.home"/>
                </a>
            </div>
        </div>
    </div>

    <script>
        // Initialize Noty defaults when page loads
        $(document).ready(function() {
            // Override Noty defaults
            Noty.overrideDefaults({
                theme: 'limitless',
                layout: 'topRight',
                type: 'alert',
                timeout: 2500
            });
        });
    </script>
</body>
</html>
